'use client'
import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'

const Company = () => {
  const [activeTab, setActiveTab] = useState('mission')
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-8 md:py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid lg:grid-cols-2 gap-6 md:gap-12 items-start mb-8 md:mb-12">
            <div className="space-y-4 md:space-y-6">
              <div className="flex items-center gap-2">
                <Image
                  src="/icons/cubeicon.svg"
                  alt="Cube Icon"
                  width={20}
                  height={20}
                  className="w-4 h-4 md:w-5 md:h-5"
                />
                <p className="text-[#454545] font-medium text-xs md:text-sm tracking-wider uppercase">WHO WE ARE</p>
              </div>
              <h1 className="font-manrope font-semibold text-[24px] md:text-[40px] leading-[120%] tracking-[0%] text-woodsmoke-950">
                Our Innovative Solutions for Your Delivery Services.
              </h1>
            </div>
            <div className="space-y-4 md:space-y-6">
              <p className="text-sm md:text-lg text-woodsmoke-600 leading-relaxed">
                Koolboks Logistics offers a range of services designed to meet diverse needs. Whether for homes, businesses, our services ensure reliable cooling without reliance on traditional energy sources.
              </p>
              <Link href="/services" className="bg-primary-500 hover:bg-primary-400 text-white px-4 md:px-6 py-2 md:py-3 rounded-lg font-medium transition-colors inline-block text-sm md:text-base">
                Our Services
              </Link>
            </div>
          </div>

          {/* Full Width Globe Image */}
          <div className="w-full">
            <div className="w-full h-[200px] md:h-[400px] relative flex items-center justify-center">
              <Image
                src="/images/globe.jpg"
                alt="Global Network"
                width={1200}
                height={400}
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-10 md:py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6 grid lg:grid-cols-2 gap-8 md:gap-16 items-center">
          <div className="relative order-2 lg:order-1">
            <div className="w-full h-[250px] md:h-[416px] rounded-2xl overflow-hidden">
              <Image
                src="/images/team.svg"
                alt="Our Team"
                width={600}
                height={400}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          <div className="h-auto md:h-[416px] flex flex-col justify-between order-1 lg:order-2">
            <div className="space-y-3 md:space-y-4">
              <h2 className="font-manrope font-semibold text-[20px] md:text-[32px] leading-[110%] tracking-[0%] text-woodsmoke-950">
                The values that drive<br />everything we do.
              </h2>
              <div className="flex gap-6 relative">
                <button
                  onClick={() => setActiveTab('mission')}
                  className={`pb-3 px-2 font-semibold transition-colors relative text-sm ${
                    activeTab === 'mission' ? 'text-primary-500' : 'text-woodsmoke-600 hover:text-woodsmoke-950'
                  }`}
                >
                  Our Mission
                  {activeTab === 'mission' && (
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary-500"></div>
                  )}
                </button>
                <button
                  onClick={() => setActiveTab('values')}
                  className={`pb-3 px-2 font-semibold transition-colors relative text-sm ${
                    activeTab === 'values' ? 'text-primary-500' : 'text-woodsmoke-600 hover:text-woodsmoke-950'
                  }`}
                >
                  Our Values
                  {activeTab === 'values' && (
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary-500"></div>
                  )}
                </button>
              </div>
            </div>

            {/* Mission Content */}
            {activeTab === 'mission' && (
              <div className="flex-1 flex flex-col justify-center space-y-4">
                <h3 className="text-xl font-bold text-woodsmoke-950">
                  Redefining Logistics for a Faster, Smarter World
                </h3>
                <p className="text-base text-woodsmoke-600 leading-relaxed">
                 For athletes, high altitude produces two contradictory effects on performance. For explosive events Physiological respiration involves the mechanisms that ensure that the composition of the functional. The long barrow was built on land previously inhabited in the Mesolithic period. It consisted of a Physical space is often conceived in three linear dimensions, although modern physicists
                </p>
              </div>
            )}

            {/* Values Content */}
            {activeTab === 'values' && (
              <div className="flex-1 grid grid-cols-2 gap-4 content-center">
                {/* Reliability */}
                <div className="space-y-2">
                  <div className="w-10 h-10 bg-[#FFF4F0] rounded-lg flex items-center justify-center">
                    <Image
                      src="/icons/Headset.svg"
                      alt="Reliability Icon"
                      width={20}
                      height={20}
                      className="w-5 h-5 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-lg font-semibold text-woodsmoke-950">Reliability</h3>
                  <p className="text-sm text-woodsmoke-600 leading-relaxed">
                    Ensuring every shipment reaches its destination safely and on time.
                  </p>
                </div>

                {/* Innovation */}
                <div className="space-y-2">
                  <div className="w-10 h-10 bg-[#FFF4F0] rounded-lg flex items-center justify-center">
                    <Image
                      src="/icons/currency.svg"
                      alt="Innovation Icon"
                      width={20}
                      height={20}
                      className="w-5 h-5 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-lg font-semibold text-woodsmoke-950">Innovation</h3>
                  <p className="text-sm text-woodsmoke-600 leading-relaxed">
                    Leveraging cutting-edge technology for smarter logistics solutions.
                  </p>
                </div>

                {/* Customer Centric Approach */}
                <div className="space-y-2">
                  <div className="w-10 h-10 bg-[#FFF4F0] rounded-lg flex items-center justify-center">
                    <Image
                      src="/icons/Clock.svg"
                      alt="Customer Centric Icon"
                      width={20}
                      height={20}
                      className="w-5 h-5 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-lg font-semibold text-woodsmoke-950">Customer Centric Approach</h3>
                  <p className="text-sm text-woodsmoke-600 leading-relaxed">
                    Prioritizing client needs with tailored logistics strategies.
                  </p>
                </div>

                {/* Sustainability */}
                <div className="space-y-2">
                  <div className="w-10 h-10 bg-[#FFF4F0] rounded-lg flex items-center justify-center">
                    <Image
                      src="/icons/Truck.svg"
                      alt="Sustainability Icon"
                      width={20}
                      height={20}
                      className="w-5 h-5 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-lg font-semibold text-woodsmoke-950">Sustainability</h3>
                  <p className="text-sm text-woodsmoke-600 leading-relaxed">
                    Committing to eco-friendly practices for a greener supply chain.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-6 md:py-6 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="w-full max-w-[980px] h-auto md:h-[106px] mx-auto grid grid-cols-2 md:flex md:justify-between items-center opacity-100 gap-4 md:gap-0">
            <div className="flex flex-col items-center justify-center text-center">
              <div className="font-manrope font-semibold text-[32px] md:text-[56px] leading-[120%] tracking-[-1%] text-center opacity-100 text-primary-500">1M+</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-base">Shipments Delivered</p>
            </div>
            <div className="flex flex-col items-center justify-center text-center">
              <div className="font-manrope font-semibold text-[32px] md:text-[56px] leading-[120%] tracking-[-1%] text-center opacity-100 text-primary-500">99%</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-base">On-time delivery Rate</p>
            </div>
            <div className="flex flex-col items-center justify-center text-center">
              <div className="font-manrope font-semibold text-[32px] md:text-[56px] leading-[120%] tracking-[-1%] text-center opacity-100 text-primary-500">25</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-base">Delivery Locations</p>
            </div>
            <div className="flex flex-col items-center justify-center text-center">
              <div className="font-manrope font-semibold text-[32px] md:text-[56px] leading-[120%] tracking-[-1%] text-center opacity-100 text-primary-500">8+</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-base">Delivery Partners</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA and Delivery Section */}
      <section className="py-8 md:py-16 lg:py-20 px-4 md:px-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:grid lg:grid-cols-12 gap-0 min-h-[300px] md:min-h-[400px]">
            {/* CTA Content Container - Mobile First */}
            <div className="lg:col-span-7 bg-[#FEEED6] rounded-2xl rounded-x-[32px] overflow-hidden h-full order-1 lg:order-2">
              <div className="h-[300px] md:h-[400px] flex flex-col justify-center px-6 md:px-8 lg:px-12 xl:px-16">
                <div className="space-y-3 md:space-y-4 lg:space-y-6">
                  <p className="text-[#666666] font-medium text-xs md:text-xs lg:text-sm tracking-[0.1em] uppercase">HIRE US FOR KOOL DELIVERY</p>
                  <h2 className="font-manrope font-semibold text-[24px] md:text-[36px] lg:text-[48px] leading-[120%] tracking-[0%] text-[#2D2D2D]">
                    Looking for the best<br />logistics transport<br />service?
                  </h2>
                  <div className="pt-3 md:pt-4 lg:pt-6">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 md:gap-4 lg:gap-6">
                      <Link href="/request-quote" className="bg-[#FF6B35] hover:bg-[#E55A2B] text-white px-4 md:px-6 lg:px-8 py-2 md:py-3 lg:py-4 rounded-[24px] font-bold text-xs md:text-sm lg:text-base transition-colors whitespace-nowrap inline-block text-center">
                        Request Quote
                      </Link>
                      <div className="flex items-center gap-2 lg:gap-3 text-[#666666]">
                        <Image
                          src="/icons/PhoneCall.svg"
                          alt="Phone"
                          width={18}
                          height={18}
                          className="w-3 h-3 md:w-4 md:h-4 lg:w-5 lg:h-5"
                        />
                        <span className="font-medium text-xs md:text-sm lg:text-base">081 342 167111</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Delivery Image Container */}
            <div className="lg:col-span-5 bg-white rounded-2xl overflow-hidden h-full order-2 lg:order-1">
              <div className="w-full h-[200px] md:h-[300px] lg:h-[400px] flex items-center justify-center">
                <Image
                  src="/images/delivery.png"
                  alt="Delivery Service"
                  width={400}
                  height={200}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>


    </div>
  )
}

export default Company
