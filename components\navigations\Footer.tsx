import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

const Footer = () => {
  return (
	<footer className="bg-black text-white">
        <div className="max-w-[1400px] mx-auto px-6 py-12">
          <div className="grid lg:grid-cols-12 gap-8">
            {/* Company Logo & Social */}
            <div className="space-y-6 lg:col-span-4">
              <div>
                <Image
                  src="/images/footer_logo.png"
                  alt="Kool Logistics Logo"
                  width={160}
                  height={50}
                  className="h-12 w-auto"
                />
              </div>
              <div className="flex space-x-3">
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <Image
                    src="/icons/x.svg"
                    alt="X (Twitter)"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <Image
                    src="/icons/linkedin.svg"
                    alt="LinkedIn"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <Image
                    src="/icons/instagram.svg"
                    alt="Instagram"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <Image
                    src="/icons/facebook.svg"
                    alt="Facebook"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#4A4A4A] rounded flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <Image
                    src="/icons/youtube.svg"
                    alt="YouTube"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
              </div>
            </div>

            {/* Say Hello */}
            <div className="space-y-4 lg:col-span-3">
              <h3 className="text-white font-semibold text-lg">Say Hello</h3>
              <div className="space-y-3 text-[#B0B0B0]">
                <p><EMAIL></p>
                <p>+1 800 100 975 20 34</p>
              </div>
            </div>

            {/* Useful Link */}
            <div className="space-y-4 lg:col-span-2">
              <h3 className="text-white font-semibold text-lg">Useful Link</h3>
              <div className="space-y-3 text-[#B0B0B0]">
                <a href="#" className="block hover:text-white transition-colors">About us</a>
                <a href="#" className="block hover:text-white transition-colors">Pricing</a>
                <Link href="/request-quote" className="block hover:text-white transition-colors">Quote</Link>
                <a href="#" className="block hover:text-white transition-colors">Contact</a>
              </div>
            </div>

            {/* Our Services */}
            <div className="space-y-4 lg:col-span-3">
              <h3 className="text-white font-semibold text-lg">Our Services</h3>
              <div className="space-y-3 text-[#B0B0B0]">
                <a href="#" className="block hover:text-white transition-colors">Logistics</a>
                <a href="#" className="block hover:text-white transition-colors">Manufacturing</a>
                <a href="#" className="block hover:text-white transition-colors">Production</a>
                <a href="#" className="block hover:text-white transition-colors">Automotive</a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="bg-[#FF6B35] py-4">
          <div className="max-w-[1400px] mx-auto px-6 flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-white text-sm">
              Copyright © 2025 KOOL LOGISTICS. All Rights Reserved.
            </p>
            <div className="flex gap-8 text-sm text-white">
              <a href="#" className="hover:text-gray-200 transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-gray-200 transition-colors">Privacy Policy</a>
            </div>
          </div>
        </div>
      </footer>
  )
}

export default Footer