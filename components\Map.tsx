import React from 'react';

interface MapProps {
  className?: string;
  height?: string;
  location?: string;
  embedUrl?: string;
}

const Map: React.FC<MapProps> = ({
  className = "",
  height = "h-[400px]",
  location = "San Francisco, CA, USA",
  embedUrl = "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3153.835434509374!2d-122.42107968468141!3d37.77492927975903!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x8085809c6c8f4459%3A0xb10ed6d9b5050fa5!2sSan%20Francisco%2C%20CA%2C%20USA!5e0!3m2!1sen!2sus!4v1642678901234!5m2!1sen!2sus"
}) => {
  return (
    <div className={`w-full ${height} rounded-[16px] overflow-hidden shadow-lg ${className}`}>
      <iframe
        src={embedUrl}
        width="100%"
        height="100%"
        style={{ border: 0 }}
        allowFullScreen
        loading="lazy"
        referrerPolicy="no-referrer-when-downgrade"
        className="grayscale"
        title={`Map of ${location}`}
      />
    </div>
  );
};

export default Map;
