'use client'

import React from 'react'
import Image from 'next/image'
import FAQ from '@/components/FAQ'
import Map from '@/components/Map'

const Contact = () => {
  return (
    <div className="min-h-screen bg-white">
      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      {/* Contact Header */}
      <section className="py-20 bg-white px-6">
        <div className="max-w-[1200px] mx-auto text-center">
          <div className="flex items-center justify-center gap-2 mb-6">
            <Image
              src="/icons/cubeicon.svg"
              alt="Cube Icon"
              width={20}
              height={20}
              className="w-5 h-5"
            />
            <p className="text-[#454545] font-medium text-sm tracking-wider uppercase">CONTACT US</p>
          </div>
          <h1 className="font-manrope font-semibold text-[40px] leading-[120%] text-center text-[#2D2D2D]">
            We&apos;re Here to Help –<br />
            Get in Touch Today!
          </h1>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-white px-6">
        <div className="max-w-[1200px] mx-auto">
          <div className="grid lg:grid-cols-2 gap-16">
            {/* Left Column - Contact Info */}
            <div>
              <h2 className="text-[32px] font-bold text-[#2D2D2D] mb-4">Get in touch</h2>
              <p className="text-[#666666] text-[16px] leading-[1.6] mb-16">
                We&apos;d love to hear from you. Our friendly team is<br />
                always here to chat.
              </p>

              <div className="grid grid-cols-2 gap-x-16 gap-y-12">
                {/* Chat with Us */}
                <div>
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center mb-4">
                    <Image
                      src="/icons/ChatCircleDots.svg"
                      alt="Chat Icon"
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                  </div>
                  <p className="text-[#666666] text-[16px] mb-2">Chat with Us</p>
                  <p className="text-[#2D2D2D] text-[18px] font-semibold">
                    (+1) 1234 567 891
                  </p>
                </div>

                {/* Visit Us */}
                <div>
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center mb-4">
                    <Image
                      src="/icons/MapPin.svg"
                      alt="Location Icon"
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                  </div>
                  <p className="text-[#666666] text-[16px] mb-2">Visit Us</p>
                  <p className="text-[#2D2D2D] text-[18px] font-semibold">
                    46 Owode-Abeokuta Road,<br />
                    Owode, Ogun
                  </p>
                </div>

                {/* Send an Email */}
                <div>
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center mb-4">
                    <Image
                      src="/icons/EnvelopeSimple.svg"
                      alt="Email Icon"
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                  </div>
                  <p className="text-[#666666] text-[16px] mb-2">Send an Email</p>
                  <p className="text-[#2D2D2D] text-[18px] font-semibold">
                    <EMAIL>
                  </p>
                </div>

                {/* Social Media */}
                <div>
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center mb-4">
                    <Image
                      src="/icons/WhatsappLogo.svg"
                      alt="Social Icon"
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                  </div>
                  <p className="text-[#666666] text-[16px] mb-4">Social Media</p>
                  <div className="flex gap-3">
                    <div className="w-10 h-10 bg-[#2D2D2D] rounded-lg flex items-center justify-center">
                      <Image
                        src="/icons/x.svg"
                        alt="X (Twitter)"
                        width={16}
                        height={16}
                        className="w-4 h-4"
                      />
                    </div>
                    <div className="w-10 h-10 bg-[#2D2D2D] rounded-lg flex items-center justify-center">
                      <Image
                        src="/icons/Linkedin.svg"
                        alt="LinkedIn"
                        width={16}
                        height={16}
                        className="w-4 h-4"
                      />
                    </div>
                    <div className="w-10 h-10 bg-[#2D2D2D] rounded-lg flex items-center justify-center">
                      <Image
                        src="/icons/Instagram.svg"
                        alt="Instagram"
                        width={16}
                        height={16}
                        className="w-4 h-4"
                      />
                    </div>
                    <div className="w-10 h-10 bg-[#2D2D2D] rounded-lg flex items-center justify-center">
                      <Image
                        src="/icons/Facebook.svg"
                        alt="Facebook"
                        width={16}
                        height={16}
                        className="w-4 h-4"
                      />
                    </div>
                    <div className="w-10 h-10 bg-[#2D2D2D] rounded-lg flex items-center justify-center">
                      <Image
                        src="/icons/Youtube.svg"
                        alt="YouTube"
                        width={16}
                        height={16}
                        className="w-4 h-4"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Contact Form */}
            <div>
              <h2 className="text-[32px] font-bold text-[#2D2D2D] mb-4">Send a Message</h2>
              <p className="text-[#666666] text-[16px] leading-[1.6] mb-8">
                We&apos;d love to hear from you. Our friendly team is always here to chat.
              </p>

              <div className="h-[600px] overflow-y-auto scrollbar-hide pr-2" style={{scrollbarWidth: 'none', msOverflowStyle: 'none'}}>
                <form className="space-y-6">
                  {/* Name Fields */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-[#2D2D2D] text-[14px] font-medium mb-2">
                        First name
                      </label>
                      <input
                        type="text"
                        placeholder="First name"
                        className="w-full px-4 py-3 border border-[#E5E5E5] rounded-[8px] text-[16px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35] transition-colors"
                      />
                    </div>
                    <div>
                      <label className="block text-[#2D2D2D] text-[14px] font-medium mb-2">
                        Last name
                      </label>
                      <input
                        type="text"
                        placeholder="Last name"
                        className="w-full px-4 py-3 border border-[#E5E5E5] rounded-[8px] text-[16px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35] transition-colors"
                      />
                    </div>
                  </div>

                  {/* Email Field */}
                  <div>
                    <label className="block text-[#2D2D2D] text-[14px] font-medium mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 border border-[#E5E5E5] rounded-[8px] text-[16px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35] transition-colors"
                    />
                  </div>

                  {/* Phone Field */}
                  <div>
                    <label className="block text-[#2D2D2D] text-[14px] font-medium mb-2">
                      Phone number
                    </label>
                    <div className="flex">
                      <select className="px-3 py-3 border border-[#E5E5E5] border-r-0 rounded-l-[8px] bg-white text-[16px] focus:outline-none focus:border-[#FF6B35]">
                        <option>NG</option>
                      </select>
                      <input
                        type="tel"
                        placeholder="+234 ************"
                        className="flex-1 px-4 py-3 border border-[#E5E5E5] rounded-r-[8px] text-[16px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35] transition-colors"
                      />
                    </div>
                  </div>

                  {/* Message Field */}
                  <div>
                    <label className="block text-[#2D2D2D] text-[14px] font-medium mb-2">
                      Message
                    </label>
                    <textarea
                      rows={4}
                      placeholder="Leave us a message..."
                      className="w-full px-4 py-3 border border-[#E5E5E5] rounded-[8px] text-[16px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35] transition-colors resize-none"
                    ></textarea>
                  </div>

                  {/* Send Message Button */}
                  <div className="pt-4">
                    <button
                      type="submit"
                      className="w-full bg-[#FF6B35] hover:bg-[#E55A2B] text-white px-6 py-3 rounded-[8px] font-semibold text-[16px] transition-colors"
                    >
                      Send Message
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-16 bg-white">
        <div className="max-w-[1200px] mx-auto px-6">
          <Map />
        </div>
      </section>

      {/* FAQ Section */}
      <FAQ />
    </div>
  )
}

export default Contact