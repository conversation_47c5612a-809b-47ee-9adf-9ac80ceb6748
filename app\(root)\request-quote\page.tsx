'use client'
import React, { useState } from 'react'
import Image from 'next/image'

const RequestQuote = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    services: [] as string[],
    shipmentOrigin: '',
    shipmentDestination: '',
    measurement: '',
    totalWeight: '',
    notes: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleServiceChange = (service: string) => {
    setFormData(prev => ({
      ...prev,
      services: [service] // Only allow one service selection
    }))
  }

  return (
    <div className="min-h-screen bg-white">
      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        input[type="checkbox"]:checked::after {
          content: "✓";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-size: 10px;
          font-weight: bold;
        }
      `}</style>
      {/* Header Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <div className="flex items-center justify-center gap-2 mb-6">
            <Image
              src="/icons/cubeicon.svg"
              alt="Cube Icon"
              width={20}
              height={20}
              className="w-5 h-5"
            />
            <p className="text-[#FF6B35] font-medium text-sm tracking-wider uppercase">REQUEST QUOTE</p>
          </div>
          <h1 className="font-manrope font-semibold text-[40px] leading-[120%] tracking-[0%] text-center text-[#2D2D2D] max-w-4xl mx-auto">
            Get Your Quote Today – Fast, Easy,<br />
            and Tailored to Your Needs!
          </h1>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-20">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-16 items-start">
            {/* Left Side - Image */}
            <div className="relative">
              <div className="w-full h-[600px] rounded-[24px] overflow-hidden">
                <Image
                  src="/images/call.svg"
                  alt="Customer service call"
                  width={600}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Right Side - Form */}
            <div className="h-[600px] overflow-y-auto scrollbar-hide" style={{scrollbarWidth: 'none', msOverflowStyle: 'none'}}>
              {/* Personal Details */}
              <div className="space-y-3">
                <h2 className="text-[20px] font-semibold text-[#2D2D2D] mb-3">Personal Details</h2>

                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-1">
                    <label className="text-[12px] font-medium text-[#666666]">First name</label>
                    <input
                      type="text"
                      name="firstName"
                      placeholder="First name"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-[#E5E5E5] rounded-[6px] text-[13px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                    />
                  </div>
                  <div className="space-y-1">
                    <label className="text-[12px] font-medium text-[#666666]">Last name</label>
                    <input
                      type="text"
                      name="lastName"
                      placeholder="Last name"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-[#E5E5E5] rounded-[6px] text-[13px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-1">
                    <label className="text-[12px] font-medium text-[#666666]">Email</label>
                    <input
                      type="email"
                      name="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-[#E5E5E5] rounded-[6px] text-[13px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                    />
                  </div>
                  <div className="space-y-1">
                    <label className="text-[12px] font-medium text-[#666666]">Phone number</label>
                    <div className="flex">
                      <select className="px-2 py-2 border border-[#E5E5E5] border-r-0 rounded-l-[6px] text-[13px] bg-white focus:outline-none focus:border-[#FF6B35]">
                        <option>NG</option>
                      </select>
                      <input
                        type="tel"
                        name="phoneNumber"
                        placeholder="+234 ************"
                        value={formData.phoneNumber}
                        onChange={handleInputChange}
                        className="flex-1 px-3 py-2 border border-[#E5E5E5] rounded-r-[6px] text-[13px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Services */}
              <div className="space-y-2 mt-4">
                <h2 className="text-[20px] font-semibold text-[#2D2D2D] mb-2">Services</h2>
                <div className="flex flex-wrap gap-4">
                  {['Fragile', 'Express Delivery', 'Insurance', 'Packaging'].map((service) => (
                    <label key={service} className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.services.includes(service)}
                        onChange={() => handleServiceChange(service)}
                        className="w-4 h-4 appearance-none border-2 border-[#E5E5E5] rounded-sm bg-white checked:bg-[#FF6B35] checked:border-[#FF6B35] focus:ring-2 focus:ring-[#FF6B35] focus:ring-opacity-50 relative"
                      />
                      <span className="text-[12px] text-[#666666]">{service}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Shipment Details */}
              <div className="space-y-2 mt-4">
                <h2 className="text-[20px] font-semibold text-[#2D2D2D] mb-2">Shipment Details</h2>

                <div className="space-y-2">
                  <div className="space-y-1">
                    <label className="text-[12px] font-medium text-[#666666]">Shipment Origin</label>
                    <input
                      type="text"
                      name="shipmentOrigin"
                      placeholder="Address"
                      value={formData.shipmentOrigin}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-[#E5E5E5] rounded-[6px] text-[13px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                    />
                  </div>

                  <div className="space-y-1">
                    <label className="text-[12px] font-medium text-[#666666]">Shipment Destination</label>
                    <input
                      type="text"
                      name="shipmentDestination"
                      placeholder="Address"
                      value={formData.shipmentDestination}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-[#E5E5E5] rounded-[6px] text-[13px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-1">
                      <label className="text-[12px] font-medium text-[#666666]">Select measurement</label>
                      <select
                        name="measurement"
                        value={formData.measurement}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-[#E5E5E5] rounded-[6px] text-[13px] bg-white focus:outline-none focus:border-[#FF6B35]"
                      >
                        <option value="">Select options</option>
                        <option value="kg">Kilograms (kg)</option>
                        <option value="lbs">Pounds (lbs)</option>
                      </select>
                    </div>
                    <div className="space-y-1">
                      <label className="text-[12px] font-medium text-[#666666]">Total Weight</label>
                      <input
                        type="text"
                        name="totalWeight"
                        placeholder="Enter size"
                        value={formData.totalWeight}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-[#E5E5E5] rounded-[6px] text-[13px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35]"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div className="space-y-1 mt-4">
                <label className="text-[12px] font-medium text-[#666666]">Notes</label>
                <textarea
                  name="notes"
                  rows={3}
                  value={formData.notes}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-[#E5E5E5] rounded-[6px] text-[13px] placeholder-[#999999] focus:outline-none focus:border-[#FF6B35] resize-none"
                />
              </div>

              {/* Submit Button */}
              <div className="pt-3">
                <button className="w-full bg-[#FF6B35] hover:bg-[#E55A2B] text-white px-6 py-3 rounded-[6px] font-semibold text-[14px] transition-colors">
                  Send Message
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>


    </div>
  )
}

export default RequestQuote
