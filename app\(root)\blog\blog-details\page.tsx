'use client'
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

const BlogDetail = () => {
  // Default blog data - using the "Technology" blog as shown in your image
  const currentBlog = {
    title: 'The Role of Technology in Enhancing Shopping and Shipping Efficiency for Nigerian Shoppers',
    image: '/images/fragile.jpg',
    content: 'For athletes, high altitude produces two contradictory effects on performance. For explosive events Physiological respiration involves the mechanisms that ensure that the composition of the functional. The long barrow was built on land previously inhabited in the Mesolithic period. It consisted of a Physical space is often conceived in three linear dimensions, although modern physicists'
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Back Button */}
      <div className="px-4 md:px-6 pt-8 pb-8">
        <Link
          href="/blog"
          className="flex items-center gap-2 text-[#FF6B35] hover:text-[#e55a2b] transition-colors"
        >
          <Image
            src="/icons/Backarrow.svg"
            alt="Back Arrow"
            width={20}
            height={20}
            className="w-5 h-5"
          />
          <span className="text-[14px] text-[#171717] font-medium">Back to Blog</span>
        </Link>
      </div>

      {/* Main Content */}
      <div className="px-4 md:px-6 pb-16">
        <div className="max-w-[1200px] mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 md:gap-12 items-start">
            {/* Left Column - Content */}
            <div className="space-y-6 md:space-y-8">
              <h1 className="font-manrope font-semibold text-[28px] md:text-[32px] lg:text-[36px] leading-[120%] tracking-[0%] text-[#2D2D2D]">
                {currentBlog.title}
              </h1>
              
              {/* Introduction with special typography */}
              <p className="font-manrope font-medium text-[20px] text-[#666666] leading-[140%] tracking-[0%] mb-8">
                In the intricate world of design, where every pixel counts, the choice of color goes far beyond aesthetics—it's a powerful tool that can influence emotions, perceptions, and user engagement. Embark on a captivating journey through the fascinating realm of color theory, offering valuable insights into how to wield this artistic palette effectively.
              </p>

              {/* The Emotional Language of Colors */}
              <div className="mb-8">
                <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-4">
                  The Emotional Language of Colors
                </h2>
                <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
                  In the intricate world of design, where every pixel counts, the choice of color goes far beyond aesthetics—it's a powerful tool that can influence emotions, perceptions, and user engagement. "Color Psychology in Design: Elevate Your Palette to" invites you to embark on a captivating journey through the fascinating realm of color theory, offering valuable insights into how to wield this artistic palette effectively.
                </p>
              </div>

              {/* Real-world Examples */}
              <div className="mb-8">
                <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-4">
                  Real-world Examples
                </h2>
                <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
                  To bring theory into practice, we showcase real-world examples of successful color palettes. Explore how global brands leverage color to communicate their identity and establish a strong visual presence. We analyze the subtle nuances of color application in websites, logos, and marketing materials, providing inspiration for your next design project.
                </p>
              </div>

              {/* Mastering Color Harmony and Contrast */}
              <div className="mb-8">
                <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-4">
                  Mastering Color Harmony and Contrast
                </h2>
                <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
                  Creating a visually appealing design involves more than just picking pretty colors. We guide you through the principles of color harmony and contrast, helping you strike the perfect balance that captivates the audience without overwhelming the senses. Learn how to create harmonious color schemes that not only please the eye but also guide the user through a seamless and enjoyable experience.
                </p>
              </div>

              {/* Symbolism and Cultural Considerations */}
              <div className="mb-8">
                <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-4">
                  Symbolism and Cultural Considerations
                </h2>
                <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
                  Colors are not only universal; they also carry cultural and contextual meanings. Uncover the symbolism inherent in different colors and how they might be perceived in various cultural contexts. Gain cultural intelligence to avoid unintentional misinterpretations and ensure your designs are inclusive and resonate with diverse audiences.
                </p>
              </div>

              {/* Practical Tips for Application */}
              <div className="mb-8">
                <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-4">
                  Practical Tips for Application
                </h2>
                <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
                  To empower designers at every skill level, we provide practical tips on applying color psychology in your projects. Whether you're working on a web design, branding, or marketing collateral, these actionable insights will help you make informed color choices that enhance your design's impact.
                </p>
              </div>

              {/* Conclusion */}
              <div className="mb-8">
                <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-4">
                  Conclusion
                </h2>
                <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
                  As we conclude our exploration into color psychology, you'll leave with a heightened palette IQ and a deeper appreciation for the strategic role color plays in design. Armed with this knowledge, you can confidently infuse your creations with a harmonious and emotionally resonant color scheme, creating designs that not only catch the eye but also leave a lasting and positive impression on your audience.
                </p>
              </div>

              {/* Author Section */}
              <div className="flex items-center gap-3 pt-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-[14px] font-semibold text-[#666666]">A</span>
                </div>
                <div>
                  <p className="text-[14px] text-[#666666] font-normal">Written by</p>
                  <p className="text-[16px] text-[#2D2D2D] font-semibold">ADMIN</p>
                </div>
              </div>
            </div>

            {/* Right Column - Image */}
            <div className="relative">
              <div className="w-full h-[300px] md:h-[400px] lg:h-[500px] relative overflow-hidden rounded-[16px] md:rounded-[20px]">
                <Image
                  src="/images/fragile.jpg"
                  alt={currentBlog.title}
                  width={600}
                  height={500}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BlogDetail
