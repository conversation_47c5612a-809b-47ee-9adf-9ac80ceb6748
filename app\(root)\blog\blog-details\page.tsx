'use client'
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

const BlogDetail = () => {
  // Default blog data - using the "Technology" blog as shown in your image
  const currentBlog = {
    title: 'The Role of Technology in Enhancing Shopping and Shipping Efficiency for Nigerian Shoppers',
    image: '/images/fragile.jpg',
    content: 'For athletes, high altitude produces two contradictory effects on performance. For explosive events Physiological respiration involves the mechanisms that ensure that the composition of the functional. The long barrow was built on land previously inhabited in the Mesolithic period. It consisted of a Physical space is often conceived in three linear dimensions, although modern physicists'
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Back Button */}
      <div className="px-4 md:px-6 pt-8 pb-8">
        <Link
          href="/blog"
          className="flex items-center gap-2 text-[#FF6B35] hover:text-[#e55a2b] transition-colors"
        >
          <Image
            src="/icons/Backarrow.svg"
            alt="Back Arrow"
            width={20}
            height={20}
            className="w-5 h-5"
          />
          <span className="text-[14px] text-[#171717] font-medium">Back to Blog</span>
        </Link>
      </div>

      {/* Main Content */}
      <div className="px-4 md:px-6 pb-16">
        <div className="max-w-[1200px] mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 md:gap-12 items-start">
            {/* Left Column - Content */}
            <div className="space-y-6 md:space-y-8">
              <h1 className="font-manrope font-semibold text-[28px] md:text-[32px] lg:text-[36px] leading-[120%] tracking-[0%] text-[#2D2D2D]">
                {currentBlog.title}
              </h1>
              
              <p className="font-manrope font-normal text-[16px] md:text-[18px] text-[#666666] leading-[150%] tracking-[0%]">
                {currentBlog.content}
              </p>

              {/* Author Section */}
              <div className="flex items-center gap-3 pt-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-[14px] font-semibold text-[#666666]">A</span>
                </div>
                <div>
                  <p className="text-[14px] text-[#666666] font-normal">Written by</p>
                  <p className="text-[16px] text-[#2D2D2D] font-semibold">ADMIN</p>
                </div>
              </div>
            </div>

            {/* Right Column - Image */}
            <div className="relative">
              <div className="w-full h-[300px] md:h-[400px] lg:h-[500px] relative overflow-hidden rounded-[16px] md:rounded-[20px]">
                <Image
                  src="/images/fragile.jpg"
                  alt={currentBlog.title}
                  width={600}
                  height={500}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BlogDetail
