'use client'
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

const BlogDetail = () => {
  return (
    <div className="min-h-screen bg-white w-[1440px] mx-auto">
      {/* Back Button */}
      <div className="px-[120px] pt-[40px] pb-[32px]">
        <Link
          href="/blog"
          className="flex items-center gap-[8px] text-[#FF6B35] hover:text-[#e55a2b] transition-colors"
        >
          <Image
            src="/icons/Backarrow.svg"
            alt="Back Arrow"
            width={20}
            height={20}
            className="w-5 h-5"
          />
          <span className="text-[14px] text-[#171717] font-medium font-manrope">Back to Blog</span>
        </Link>
      </div>

      {/* Hero Section */}
      <div className="px-[120px] pb-[80px]">
        <div className="bg-white rounded-[20px] p-[40px] flex items-center gap-[40px]">
          {/* Left Content */}
          <div className="flex-1">
            <h1 className="font-manrope font-semibold text-[32px] leading-[120%] tracking-[0%] text-[#2D2D2D] mb-[24px]">
              The Role of Technology in Enhancing Shopping and Shipping Efficiency for Nigerian Shoppers
            </h1>

            <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%] mb-[32px]">
              For athletes, high altitude produces two contradictory effects on performance. For explosive events Physiological respiration involves the mechanisms that ensure that the composition of the functional. The long barrow was built on land previously inhabited in the Mesolithic period. It consisted of a Physical space is often conceived in three linear dimensions, although modern physicists
            </p>

            {/* Author Section */}
            <div className="flex items-center gap-[12px]">
              <div className="w-[40px] h-[40px] bg-gray-200 rounded-full flex items-center justify-center">
                <span className="text-[14px] font-semibold text-[#666666] font-manrope">A</span>
              </div>
              <div>
                <p className="text-[12px] text-[#666666] font-normal font-manrope">Written by</p>
                <p className="text-[14px] text-[#2D2D2D] font-semibold font-manrope">ADMIN</p>
              </div>
            </div>
          </div>

          {/* Right Image */}
          <div className="w-[400px] h-[300px] relative overflow-hidden rounded-[16px] flex-shrink-0">
            <Image
              src="/images/fragile.jpg"
              alt="Technology in Shopping and Shipping"
              width={400}
              height={300}
              className="w-full h-full object-cover"
            />
            {/* FRAGILE label overlay */}
            <div className="absolute top-[16px] left-[16px] bg-white px-[12px] py-[4px] rounded-[4px]">
              <span className="text-[12px] font-bold text-[#FF6B35] font-manrope">FRAGILE</span>
            </div>
          </div>
        </div>
      </div>

              {/* Main Content Area */}
      <div className="px-[120px] pb-[80px]">
        <div className="max-w-[600px]">
          {/* Introduction with special typography */}
          <p className="font-manrope font-medium text-[20px] text-[#666666] leading-[140%] tracking-[0%] mb-[48px]">
            In the intricate world of design, where every pixel counts, the choice of color goes far beyond aesthetics it's a powerful tool that can influence emotions, perceptions, and user engagement. Embark on a captivating journey through the fascinating realm of color theory, offering valuable insights into how to wield this artistic palette effectively.
          </p>

          {/* The Emotional Language of Colors */}
          <div className="mb-[48px]">
            <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-[16px]">
              The Emotional Language of Colors
            </h2>
            <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
              In the intricate world of design, where every pixel counts, the choice of color goes far beyond aesthetics it's a powerful tool that can influence emotions, perceptions, and user engagement. "Color Psychology in Design: Elevate Your Palette to" invites you to embark on a captivating journey through the fascinating realm of color theory, offering valuable insights into how to wield this artistic palette effectively.
            </p>
          </div>

          {/* Real-world Examples */}
          <div className="mb-[48px]">
            <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-[16px]">
              Real-world Examples
            </h2>
            <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
              To bring theory into practice, we showcase real-world examples of successful color palettes. Explore how global brands leverage color to communicate their identity and establish a strong visual presence. We analyze the subtle nuances of color application in websites, logos, and marketing materials, providing inspiration for your next design project.
            </p>
          </div>

          {/* Mastering Color Harmony and Contrast */}
          <div className="mb-[48px]">
            <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-[16px]">
              Mastering Color Harmony and Contrast
            </h2>
            <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
              Creating a visually appealing design involves more than just picking pretty colors. We guide you through the principles of color harmony and contrast, helping you strike the perfect balance that captivates the audience without overwhelming the senses. Learn how to create harmonious color schemes that not only please the eye but also guide the user through a seamless and enjoyable experience.
            </p>
          </div>

          {/* Symbolism and Cultural Considerations */}
          <div className="mb-[48px]">
            <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-[16px]">
              Symbolism and Cultural Considerations
            </h2>
            <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
              Colors are not only universal; they also carry cultural and contextual meanings. Uncover the symbolism inherent in different colors and how they might be perceived in various cultural contexts. Gain cultural intelligence to avoid unintentional misinterpretations and ensure your designs are inclusive and resonate with diverse audiences.
            </p>
          </div>

          {/* Practical Tips for Application */}
          <div className="mb-[48px]">
            <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-[16px]">
              Practical Tips for Application
            </h2>
            <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
              To empower designers at every skill level, we provide practical tips on applying color psychology in your projects. Whether you're working on a web design, branding, or marketing collateral, these actionable insights will help you make informed color choices that enhance your design's impact.
            </p>
          </div>

          {/* Conclusion */}
          <div className="mb-[48px]">
            <h2 className="font-manrope font-semibold text-[24px] text-[#2D2D2D] leading-[130%] tracking-[0%] mb-[16px]">
              Conclusion
            </h2>
            <p className="font-manrope font-normal text-[16px] text-[#666666] leading-[150%] tracking-[0%]">
              As we conclude our exploration into color psychology, you'll leave with a heightened palette IQ and a deeper appreciation for the strategic role color plays in design. Armed with this knowledge, you can confidently infuse your creations with a harmonious and emotionally resonant color scheme, creating designs that not only catch the eye but also leave a lasting and positive impression on your audience.
            </p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-[#2D2D2D] px-[120px] py-[80px]">
        <div className="flex justify-between">
          {/* Logo */}
          <div className="flex items-center gap-[8px]">
            <div className="w-[40px] h-[40px] bg-white rounded-[8px] flex items-center justify-center">
              <span className="text-[20px] font-bold text-[#2D2D2D]">K</span>
            </div>
            <div>
              <div className="text-white font-bold text-[16px] font-manrope">KOOL</div>
              <div className="text-white text-[12px] font-manrope">LOGISTICS</div>
            </div>
          </div>

          {/* Footer Links */}
          <div className="flex gap-[120px]">
            {/* Say Hello */}
            <div>
              <h3 className="text-white font-semibold text-[16px] font-manrope mb-[24px]">Say Hello</h3>
              <div className="space-y-[16px]">
                <p className="text-[#CCCCCC] text-[14px] font-manrope"><EMAIL></p>
                <p className="text-[#CCCCCC] text-[14px] font-manrope">+234 (0) 816 319 1</p>
              </div>
            </div>

            {/* Useful Links */}
            <div>
              <h3 className="text-white font-semibold text-[16px] font-manrope mb-[24px]">Useful Links</h3>
              <div className="space-y-[16px]">
                <p className="text-[#CCCCCC] text-[14px] font-manrope">Logistics</p>
                <p className="text-[#CCCCCC] text-[14px] font-manrope">Pricing</p>
                <p className="text-[#CCCCCC] text-[14px] font-manrope">Quote</p>
                <p className="text-[#CCCCCC] text-[14px] font-manrope">Contact</p>
              </div>
            </div>

            {/* Our Services */}
            <div>
              <h3 className="text-white font-semibold text-[16px] font-manrope mb-[24px]">Our Services</h3>
              <div className="space-y-[16px]">
                <p className="text-[#CCCCCC] text-[14px] font-manrope">Warehousing</p>
                <p className="text-[#CCCCCC] text-[14px] font-manrope">Packaging</p>
                <p className="text-[#CCCCCC] text-[14px] font-manrope">Automotive</p>
              </div>
            </div>
          </div>
        </div>

        {/* Social Icons */}
        <div className="flex gap-[16px] mt-[60px]">
          <div className="w-[40px] h-[40px] bg-[#404040] rounded-[8px] flex items-center justify-center">
            <span className="text-white text-[16px]">f</span>
          </div>
          <div className="w-[40px] h-[40px] bg-[#404040] rounded-[8px] flex items-center justify-center">
            <span className="text-white text-[16px]">t</span>
          </div>
          <div className="w-[40px] h-[40px] bg-[#404040] rounded-[8px] flex items-center justify-center">
            <span className="text-white text-[16px]">in</span>
          </div>
          <div className="w-[40px] h-[40px] bg-[#404040] rounded-[8px] flex items-center justify-center">
            <span className="text-white text-[16px]">ig</span>
          </div>
          <div className="w-[40px] h-[40px] bg-[#404040] rounded-[8px] flex items-center justify-center">
            <span className="text-white text-[16px]">yt</span>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-[40px] pt-[40px] border-t border-[#404040]">
          <p className="text-[#CCCCCC] text-[14px] font-manrope">Copyright © 2024 Kool Logistics. All rights reserved.</p>
        </div>
      </div>
    </div>
  )
}

export default BlogDetail
