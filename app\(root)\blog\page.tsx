'use client'
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

const Blog = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <section className="py-12 md:py-16 lg:py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid lg:grid-cols-2 gap-8 md:gap-12 lg:gap-16 items-center">
            {/* Left Content */}
            <div className="space-y-6 md:space-y-8">
              <div className="flex items-center gap-2">
                <Image
                  src="/icons/cubeicon.svg"
                  alt="Cube Icon"
                  width={20}
                  height={20}
                  className="w-4 h-4 md:w-5 md:h-5"
                />
                <p className="text-primary-500 font-medium text-xs md:text-sm tracking-wider uppercase">BLOG</p>
              </div>
              <h1 className="font-manrope font-semibold text-[32px] md:text-[40px] lg:text-[48px] leading-[120%] tracking-[0%] text-woodsmoke-950">
                Insights into the logistics industry
              </h1>
            </div>

            {/* Right Content */}
            <div className="space-y-4 md:space-y-6">
              <p className="text-base md:text-lg text-woodsmoke-600 leading-relaxed">
                Discover how KOOL LOGISTICS has transformed logistics challenges into success stories for businesses across diverse sectors
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-8 md:py-12 lg:py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">

            {/* Blog Post 1 */}
            <article className="bg-white rounded-2xl overflow-hidden border border-gray-100 hover:shadow-lg transition-shadow duration-300">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src="/images/blog-1.jpg"
                  alt="Supply Chain Optimization"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 text-sm text-woodsmoke-500">
                  <span>March 15, 2024</span>
                  <span>•</span>
                  <span>5 min read</span>
                </div>
                <h3 className="font-manrope font-semibold text-xl text-woodsmoke-950 leading-[130%]">
                  How to Optimize Your Supply Chain for Maximum Efficiency
                </h3>
                <p className="text-woodsmoke-600 leading-relaxed">
                  Learn the key strategies and technologies that can transform your supply chain operations and reduce costs significantly.
                </p>
                <Link
                  href="/blog/supply-chain-optimization"
                  className="inline-flex items-center gap-2 text-primary-500 font-medium hover:text-primary-600 transition-colors"
                >
                  Read More
                  <Image
                    src="/icons/arrow-right.svg"
                    alt="Arrow Right"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </Link>
              </div>
            </article>

            {/* Blog Post 2 */}
            <article className="bg-white rounded-2xl overflow-hidden border border-gray-100 hover:shadow-lg transition-shadow duration-300">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src="/images/blog-2.jpg"
                  alt="Last Mile Delivery"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 text-sm text-woodsmoke-500">
                  <span>March 12, 2024</span>
                  <span>•</span>
                  <span>7 min read</span>
                </div>
                <h3 className="font-manrope font-semibold text-xl text-woodsmoke-950 leading-[130%]">
                  The Future of Last-Mile Delivery: Trends and Innovations
                </h3>
                <p className="text-woodsmoke-600 leading-relaxed">
                  Explore emerging technologies and strategies that are reshaping the last-mile delivery landscape.
                </p>
                <Link
                  href="/blog/last-mile-delivery"
                  className="inline-flex items-center gap-2 text-primary-500 font-medium hover:text-primary-600 transition-colors"
                >
                  Read More
                  <Image
                    src="/icons/arrow-right.svg"
                    alt="Arrow Right"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </Link>
              </div>
            </article>

            {/* Blog Post 3 */}
            <article className="bg-white rounded-2xl overflow-hidden border border-gray-100 hover:shadow-lg transition-shadow duration-300">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src="/images/blog-3.jpg"
                  alt="Sustainable Logistics"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 text-sm text-woodsmoke-500">
                  <span>March 10, 2024</span>
                  <span>•</span>
                  <span>6 min read</span>
                </div>
                <h3 className="font-manrope font-semibold text-xl text-woodsmoke-950 leading-[130%]">
                  Building Sustainable Logistics: Environmental Impact Solutions
                </h3>
                <p className="text-woodsmoke-600 leading-relaxed">
                  Discover how companies are reducing their carbon footprint through innovative logistics practices.
                </p>
                <Link
                  href="/blog/sustainable-logistics"
                  className="inline-flex items-center gap-2 text-primary-500 font-medium hover:text-primary-600 transition-colors"
                >
                  Read More
                  <Image
                    src="/icons/arrow-right.svg"
                    alt="Arrow Right"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </Link>
              </div>
            </article>

            {/* Blog Post 4 */}
            <article className="bg-white rounded-2xl overflow-hidden border border-gray-100 hover:shadow-lg transition-shadow duration-300">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src="/images/blog-4.jpg"
                  alt="Warehouse Management"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 text-sm text-woodsmoke-500">
                  <span>March 8, 2024</span>
                  <span>•</span>
                  <span>4 min read</span>
                </div>
                <h3 className="font-manrope font-semibold text-xl text-woodsmoke-950 leading-[130%]">
                  Smart Warehouse Management: Technology Integration Guide
                </h3>
                <p className="text-woodsmoke-600 leading-relaxed">
                  Learn how to implement smart technologies to streamline your warehouse operations and boost productivity.
                </p>
                <Link
                  href="/blog/warehouse-management"
                  className="inline-flex items-center gap-2 text-primary-500 font-medium hover:text-primary-600 transition-colors"
                >
                  Read More
                  <Image
                    src="/icons/arrow-right.svg"
                    alt="Arrow Right"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </Link>
              </div>
            </article>

            {/* Blog Post 5 */}
            <article className="bg-white rounded-2xl overflow-hidden border border-gray-100 hover:shadow-lg transition-shadow duration-300">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src="/images/blog-5.jpg"
                  alt="E-commerce Logistics"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 text-sm text-woodsmoke-500">
                  <span>March 5, 2024</span>
                  <span>•</span>
                  <span>8 min read</span>
                </div>
                <h3 className="font-manrope font-semibold text-xl text-woodsmoke-950 leading-[130%]">
                  E-commerce Logistics: Scaling Your Online Business Delivery
                </h3>
                <p className="text-woodsmoke-600 leading-relaxed">
                  Essential strategies for managing logistics as your e-commerce business grows and customer expectations rise.
                </p>
                <Link
                  href="/blog/ecommerce-logistics"
                  className="inline-flex items-center gap-2 text-primary-500 font-medium hover:text-primary-600 transition-colors"
                >
                  Read More
                  <Image
                    src="/icons/arrow-right.svg"
                    alt="Arrow Right"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </Link>
              </div>
            </article>

            {/* Blog Post 6 */}
            <article className="bg-white rounded-2xl overflow-hidden border border-gray-100 hover:shadow-lg transition-shadow duration-300">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src="/images/blog-6.jpg"
                  alt="Cold Chain Logistics"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 text-sm text-woodsmoke-500">
                  <span>March 3, 2024</span>
                  <span>•</span>
                  <span>6 min read</span>
                </div>
                <h3 className="font-manrope font-semibold text-xl text-woodsmoke-950 leading-[130%]">
                  Cold Chain Logistics: Maintaining Product Integrity
                </h3>
                <p className="text-woodsmoke-600 leading-relaxed">
                  Understanding the critical aspects of temperature-controlled logistics for pharmaceutical and food industries.
                </p>
                <Link
                  href="/blog/cold-chain-logistics"
                  className="inline-flex items-center gap-2 text-primary-500 font-medium hover:text-primary-600 transition-colors"
                >
                  Read More
                  <Image
                    src="/icons/arrow-right.svg"
                    alt="Arrow Right"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </Link>
              </div>
            </article>

          </div>
        </div>
      </section>

      {/* Load More Section */}
      <section className="py-8 md:py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6 text-center">
          <button className="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3 rounded-lg font-medium transition-colors">
            Load More Articles
          </button>
        </div>
      </section>
    </div>
  )
}

export default Blog